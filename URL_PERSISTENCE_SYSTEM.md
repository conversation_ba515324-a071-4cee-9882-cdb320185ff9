# URL Persistence System

## Overview

The URL Persistence System automatically detects, categorizes, and stores URLs mentioned in chat conversations, making them available across different chat contexts (MainChat, BriefChat, Task View). This ensures that URLs mentioned during campaign planning are accessible when generating briefs and emails.

## Features

- **Automatic URL Detection**: Detects URLs in chat messages and categorizes them by type
- **Cross-Context Sharing**: URLs from MainChat are available in BriefChat and Task View
- **Smart Categorization**: Automatically categorizes URLs as Button/CTA, Product, Collection, Image, or General
- **Persistent Storage**: URLs are stored in organization settings and persist across sessions
- **Prompt Integration**: URLs are automatically included in AI prompt context

## How It Works

### 1. Automatic Detection

When users send messages in MainChat or BriefChat, the system:
- Scans the message for URLs using regex patterns
- Analyzes the context to determine URL type
- Automatically saves new URLs to organization settings
- Avoids duplicates by checking existing URLs

### 2. URL Categorization

URLs are automatically categorized based on:
- **Product**: URLs containing `/products/` or `/product/`
- **Collection**: URLs containing `/collections/` or `/collection/`
- **Button/CTA**: Context mentions "button", "cta", "shop now", "buy now", etc.
- **Image**: URLs ending in image extensions or context mentions "image", "photo"
- **General**: All other URLs

### 3. Cross-Context Availability

URLs stored from MainChat are automatically available in:
- BriefChat conversations (via `{CAMPAIGN_URLS}` prompt tag)
- Task View contexts
- Email generation processes

## Implementation Details

### Core Components

1. **URLDetectionService** (`public/client/services/urlDetectionService.ts`)
   - Main service for URL detection and management
   - Handles storage, retrieval, and categorization

2. **MemoryToolHandler** (Enhanced)
   - Extended to also detect URLs in memory content
   - Dual functionality for both memories and URLs

3. **Prompt Context Service** (Enhanced)
   - Added `getCampaignURLs()` method
   - Includes `{CAMPAIGN_URLS}` tag replacement

## Usage

### For Developers

#### Integrating URL Detection

```typescript
import { URLDetectionService } from '../services/urlDetectionService';

// Detect and save URLs from text
const detectedURLs = await URLDetectionService.detectAndSaveURLs(messageText);

// Get stored URLs
const storedURLs = await URLDetectionService.getStoredURLs();
```

### For Users

1. **Automatic Detection**: Simply mention URLs in your chat messages
   - "Check out our new product at https://store.com/products/new-item"
   - "Use this button URL https://store.com/shop-now for the CTA"

2. **Cross-Context Usage**: URLs mentioned in MainChat will automatically be available when:
   - Working on briefs in BriefChat
   - Generating emails
   - Using task views

## Prompt Integration

URLs are automatically included in AI prompts using the `{CAMPAIGN_URLS}` tag:

```
Campaign URLs:
1. [BUTTON] https://store.com/shop-now - Call-to-action button URL
2. [PRODUCT] https://store.com/products/new-item - Product page URL for use in email campaigns
3. [COLLECTION] https://store.com/collections/winter - Collection page URL for category links
```

## Storage

URLs are stored in the `campaignURLs` organization setting as JSON:

```json
[
  {
    "url": "https://store.com/products/new-item",
    "type": "product",
    "description": "Product page URL for use in email campaigns",
    "context": "Check out our new product at https://store.com/products/new-item",
    "timestamp": "2024-12-01T10:30:00.000Z"
  }
]
```

## Configuration

- **Maximum URLs**: 100 URLs per organization (configurable)
- **Duplicate Detection**: Based on exact URL match
- **Context Length**: 100 characters before and after URL for context extraction

## Testing

Test the system by:
1. Running `npm run start:dev`
2. Mentioning URLs in MainChat messages
3. Checking that URLs appear in BriefChat context
4. Verifying URLs are used correctly in generated emails

## Future Enhancements

- URL management UI for manual control
- URL validation and accessibility checking
- URL categorization based on page content analysis
- URL usage analytics
- URL expiration and cleanup
