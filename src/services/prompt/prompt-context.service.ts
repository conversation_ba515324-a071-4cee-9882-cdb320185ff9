import {BindingScope, inject, injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OrganizationRepository, OrganizationSegmentRepository, UnlayerComponentRepository, ImageRepository, PromptTemplateRepository, OrganizationPlannerPlanRepository, MetricRepository, OrganizationMetricRepository} from '../../repositories';
import {PlannerCampaign, TaskWithRelations} from '../../models';
import {OrganizationSettings} from '../../models/organization-settings.model';
import {KlaviyoMetricsService} from '../metrics/klaviyo-metrics.service';
import {EcommerceMetricController} from '../../controllers/ecommerce-metric.controller';
import {DEFAULT_PLAN_STRATEGY, DEFAULT_TOOL_DATA_LOOKUP, DEFAULT_TOOL_BEST_SELLERS} from './tag_defaults';
import {PromptCacheService} from './prompt-cache.service';
import {PROMPT_TAGS} from './tags';

/**
 * Service to manage prompt context and tag replacements.
 * Uses caching to improve performance for metrics and context data
 * with a TTL of 24 hours.
 */

@injectable({scope: BindingScope.TRANSIENT})
export class PromptContextService {
  constructor(
    @repository(OrganizationRepository)
    private organizationRepository: OrganizationRepository,
    @inject('datasources.dev_db')
    private devDbDataSource: any,
    @repository(OrganizationSegmentRepository)
    private organizationSegmentRepository: OrganizationSegmentRepository,
    @repository(UnlayerComponentRepository)
    private unlayerComponentRepository: UnlayerComponentRepository,
    @repository(ImageRepository)
    private imageRepository: ImageRepository,
    @repository(PromptTemplateRepository)
    private promptTemplateRepository: PromptTemplateRepository,
    @repository(MetricRepository)
    private metricRepository: MetricRepository,
	@service(KlaviyoMetricsService)
	private klaviyoMetricsService: KlaviyoMetricsService,
	@inject('controllers.EcommerceMetricController')
	private ecommerceMetricController: EcommerceMetricController,
	@repository(OrganizationPlannerPlanRepository)
	private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
    @service(PromptCacheService)
    private promptCacheService: PromptCacheService,
    @repository(OrganizationMetricRepository)
    private organizationMetricRepository: OrganizationMetricRepository,

  ) {}

  private async getAvailableMetrics(orgId: number): Promise<string> {
    // First get organization metrics
    const orgMetrics = await this.organizationMetricRepository.find({
		where: {
		  and: [
			{ orgId: orgId },
			{ lastRunDate: { neq: null as unknown as string } }
		  ]
		}
	  });



    // Get metric IDs from org metrics
    const metricIds = orgMetrics.map(om => om.metricId);

    // Then filter metrics that have includeInPrompt = true
    const metrics = await this.metricRepository.find({
      where: {
        and: [
          { id: { inq: metricIds } },
          { includeInPrompt: true }
        ]
      }
    });

    if (metrics.length === 0) {
      return 'No metrics available';
    }

	console.log(`Available metrics for org ${orgId}:`, metrics);
    return metrics.map(metric =>
      `${metric.name}: ${metric.description || 'No description available'}`
    ).join('\n');
  }

  /**
   * Retrieves and formats campaign URLs from organization settings
   * URLs are stored as JSON objects in the OrganizationSettings with key 'campaignURLs'
   *
   * @param orgId - Organization ID to fetch URLs for
   * @returns Formatted string of URLs for prompt context
   */
  private async getCampaignURLs(orgId: number): Promise<string> {
    try {
      const settings: OrganizationSettings[] = await this.organizationRepository.organizationSettings(orgId).find();
      const urlsSetting = settings.find(s => s.key === 'campaignURLs');

      if (!urlsSetting || !urlsSetting.value) {
        return 'No campaign URLs stored';
      }

      const urls = JSON.parse(urlsSetting.value);

      if (!Array.isArray(urls) || urls.length === 0) {
        return 'No campaign URLs available';
      }

      // Format URLs for prompt context
      const formattedURLs = urls.map((urlData, index) => {
        const timestamp = urlData.timestamp ? new Date(urlData.timestamp).toLocaleDateString() : 'Unknown date';
        const url = urlData.url || 'No URL';
        const type = urlData.type || 'General';
        const description = urlData.description || 'No description';
        return `${index + 1}. [${type.toUpperCase()}] ${url} - ${description} (Added: ${timestamp})`;
      }).join('\n');

      return `Campaign URLs:\n${formattedURLs}`;
    } catch (error) {
      console.error('Error retrieving campaign URLs:', error);
      return 'Error retrieving campaign URLs';
    }
  }

  /**
   * Retrieves and formats brand memories from organization settings
   * Memories are stored as JSON objects in the OrganizationSettings with key 'brandMemories'
   *
   * @param orgId - Organization ID to fetch memories for
   * @returns Formatted string of memories for prompt context
   */
  private async getBrandMemories(orgId: number): Promise<string> {
    try {
      const settings: OrganizationSettings[] = await this.organizationRepository.organizationSettings(orgId).find();
      const memoriesSetting = settings.find(s => s.key === 'brandMemories');

      if (!memoriesSetting || !memoriesSetting.value) {
        return 'No brand memories stored';
      }

      const memories = JSON.parse(memoriesSetting.value);

      if (!Array.isArray(memories) || memories.length === 0) {
        return 'No brand memories available';
      }

      // Format memories for prompt context
      const formattedMemories = memories.map((memory, index) => {
        const timestamp = memory.timestamp ? new Date(memory.timestamp).toLocaleDateString() : 'Unknown date';
        const content = memory.content || memory.text || 'No content';
        const type = memory.type || 'General';
        return `${index + 1}. [${type}] ${timestamp}: ${content}`;
      }).join('\n');

      return `Brand Memories:\n${formattedMemories}`;
    } catch (error) {
      console.error('Error retrieving brand memories:', error);
      return 'Error retrieving brand memories';
    }
  }

  /**
   * Replaces prompt tags with actual values from various sources including cached data
   *
   * @param prompt - The prompt template string with tags to be replaced
   * @param orgId - Organization ID to fetch data for
   * @param userPrompt - Optional user-provided prompt text
   * @param campaign - Optional campaign data to include in the prompt
   * @param task - Optional task data to include in the prompt
   * @param extraMetricsData - Optional pre-fetched metrics data
   * @returns The prompt with all tags replaced with actual values
   */
  async replacePromptTags(prompt: string, orgId: number, userPrompt?: string, campaign?: PlannerCampaign, task?: TaskWithRelations, extraMetricsData?: any): Promise<string> {
    const data = await this.getAdditionalContextData(orgId, prompt);

	if (this.promptRequiresMetrics(prompt) && this.extraDataMissingMetrics(extraMetricsData)) {
		const klaviyoMetrics = await this.getMetricsData(orgId, prompt);
		extraMetricsData = {
			...(extraMetricsData || {}),
			...klaviyoMetrics
		};
	}

    if(!data.emailBranding) {
      data.emailBranding = '';
    }

    if (campaign) {
      data.campaignType = campaign.type;
      data.campaignTargetSegment = campaign.targetSegment;
      data.campaignName = campaign.name;
      data.campaignDescription = campaign.description;
      data.campaignPromotionTitle = campaign.promotionTitle;
      data.campaignPromotionDescription = campaign.promotionDescription;
    }

    if (task) {
      data.taskType = task.taskType;
      data.taskDescription = task.description;
      data.taskScheduledDate = task.scheduledDate;
      data.emailBrief = task.taskSteps?.find(step => step.name === 'Content')?.data;
    }

    // Replace all available tags
    return prompt
      .replace(/{USER_INPUT}/g, userPrompt || '')
      .replace(/{BESTSELLERS}/g, data.bestSellers || '')
      .replace(/{CURRENT_DATE}/g, new Date().toISOString())
      .replace(/{CAMPAIGN_TYPE}/g, data.campaignType || '')
      .replace(/{CAMPAIGN_TARGET_SEGMENT}/g, data.campaignTargetSegment || '')
      .replace(/{CAMPAIGN_NAME}/g, data.campaignName || '')
      .replace(/{CAMPAIGN_DESCRIPTION}/g, data.campaignDescription || '')
      .replace(/{CAMPAIGN_PROMOTION_TITLE}/g, data.campaignPromotionTitle || '')
      .replace(/{CAMPAIGN_PROMOTION_DESCRIPTION}/g, data.campaignPromotionDescription || '')
      .replace(/{CAMPAIGN_TIMEFRAME}/g, data.campaignTimeframe || '1 month')
      .replace(/{STORE_NAME}/g, data.storeName || '')
      .replace(/{STORE_DESCRIPTION}/g, data.storeDescription || '')
      .replace(/{STORE_LANGUAGE}/g, data.storeLanguage || '')
      .replace(/{STORE_AOV}/g, data.storeAOV || '')
      .replace(/{PRODUCT_CATEGORIES}/g, data.productCategories || '')
      .replace(/{STORE_BESTSELLERS}/g, data.bestSellers || '')
      .replace(/{SEGMENTS_DATA}/g, data.segmentsData || '')
      .replace(/{BUSINESS_GOALS}/g, data.businessGoals || '')
      .replace(/{HAS_LOYALTY}/g, data.hasLoyalty ? 'This store has loyalty activated' : 'This store does not have loyalty activated')
      .replace(/{HAS_SMS}/g, data.hasSMS ? 'This store has SMS activated' : 'This store does not have SMS activated')
	  .replace(/{HAS_EMAIL_GEN}/g, data.hasEmailGeneration || 'This store does not have email generation activated')
      .replace(/{EMAIL_BRANDING}/g, data.emailBranding || '')
      .replace(/{IDEAL_CUSTOMER}/g, data.idealCustomer || '')
      .replace(/{CUSTOMER_PROBLEMS}/g, data.customerProblems || '')
      .replace(/{CUSTOMER_BEHAVIOR}/g, data.customerBehavior || '')
      .replace(/{EMAIL_BRIEF}/g, data.emailBrief || '')
      .replace(/{MARKETING_SUCCESS}/g, data.marketingSuccess || '')
      .replace(/{HAS_SUBSCRIPTIONS}/g, data.hasSubscriptions ? 'This store has subscriptions activated' : 'This store does not have subscriptions activated')
      .replace(/{BRAND_CUSTOM_INSTRUCTIONS}/g, data.brandCustomInstructions || '')
      .replace(/{PLAN_STRATEGY}/g, data.planStrategy || DEFAULT_PLAN_STRATEGY)
      .replace(/{INFLUENCER_COLLABORATION}/g, data.influencerCollaboration || '')
      .replace(/{EMAIL_TEMPLATES}/g, data.emailTemplates || '[]')
      .replace(/{COMPONENT_LIBRARY}/g, data.componentLibrary || '')
      .replace(/{COMPONENT_LIBRARY_SUMMARY}/g, data.componentLibrarySummary || '')
      .replace(/{BRAND_IMAGES}/g, data.brandImages || '')
      .replace(/{TOP_5_SUBJECT_LINES}/g, extraMetricsData?.top5SubjectLines || '')
      .replace(/{KLAVIYO_CAMPAIGNS_ALL_L12}/g, extraMetricsData?.klaviyoCampaignsAll || '')
      .replace(/{KLAVIYO_CAMPAIGNS_12_MONTH_AGO}/g, extraMetricsData?.klaviyoCampaigns12MonthAgo || '')
      .replace(/{LAST_50_CAMPAIGNS}/g, extraMetricsData?.last50Campaigns || '')
      .replace(/{KLAVIYO_CAMPAIGN_SUMMARY}/g, extraMetricsData?.klaviyoCampaignSummary || '')
      .replace(/{KLAVIYO_LAST_WEEK}/g, extraMetricsData?.klaviyoLastWeek || '')
      .replace(/{KLAVIYO_LAST_MONTH}/g, extraMetricsData?.klaviyoLastMonth || '')
      .replace(/{AVAILABLE_METRICS}/g, await this.getAvailableMetrics(orgId))
      .replace(/{MEMORY}/g, await this.getBrandMemories(orgId))
      .replace(/{CAMPAIGN_URLS}/g, await this.getCampaignURLs(orgId))
      .replace(/{TOOL_DATA_LOOKUP}/g, DEFAULT_TOOL_DATA_LOOKUP)
      .replace(/{TOOL_BEST_SELLERS}/g, DEFAULT_TOOL_BEST_SELLERS);
  }

  /**
   * Gets additional context data for an organization
   * Uses cached data when available to improve performance
   *
   * @param orgId - Organization ID to fetch data for
   * @param prompt - The prompt text to analyze for required tags
   * @returns Object containing all context data for tag replacement
   */
  private async getAdditionalContextData(orgId: number, prompt: string): Promise<any> {
    const organization = await this.organizationRepository.findById(orgId);
    const settings: OrganizationSettings[] = await this.organizationRepository.organizationSettings(orgId).find();
    const getSetting = (key: string) => settings.find(s => s.key === key)?.value;

    // Try to get cached context data
    let cachedContextData;
    try {
      cachedContextData = await this.promptCacheService.getCachedContextData(orgId);
    } catch (error) {
      console.error(`Error retrieving cached context data for org ${orgId}:`, error);
      // Continue execution if cache retrieval fails
    }

    // Use cached averageAov if available
    let averageAov;
    if (cachedContextData && cachedContextData.averageAov) {
      averageAov = cachedContextData.averageAov;
    } else {
      const getAverageAovQuery = await this.devDbDataSource.execute(`
        SELECT
          AVG(CASE
            WHEN aov IS NOT NULL AND aov > 0
            THEN aov
            END) as average_aov
        FROM raleonuseridentity
        WHERE
          orgid = $1
      `, [orgId]);

      averageAov = getAverageAovQuery[0]?.average_aov;

      // Cache the context data for future use
      try {
        await this.promptCacheService.cacheContextData(orgId);
      } catch (error) {
        console.error(`Error caching context data for org ${orgId}:`, error);
        // Continue execution if caching fails
      }
    }

    // Gather segments
    const segments = await this.organizationSegmentRepository.find({
      where: {
        orgId: orgId
      }
    });

    const segmentsData = segments?.map(segment => `${segment.name}: ${segment.description}`).join('\n');

    // Fetch brand images with non-null and non-empty imageType
    const brandImages = await this.imageRepository.find({
      where: {
        orgId: orgId,
        imageType: {
          neq: '',
        }
      }
    });

    // Filter and format brand images
    const filteredBrandImages = brandImages.filter(image =>
      image.imageType !== null &&
      image.imageType !== undefined &&
      image.imageType !== '');

    // Group images by imageType (AI Tag)
    const groupedImages: Record<string, any[]> = {};
    filteredBrandImages.forEach(image => {
      const imageType = image.imageType || 'Uncategorized';
      if (!groupedImages[imageType]) {
        groupedImages[imageType] = [];
      }
      groupedImages[imageType].push({
        url: image.url,
        width: image.width || 0,
        height: image.height || 0
      });
    });

    // Format brand images data
    let brandImagesFormatted = filteredBrandImages.length > 0 ?
      '' : 'No brand images with AI Tags found. Please add AI Tags to your images in the Assets section.';

    if (filteredBrandImages.length > 0) {
      Object.entries(groupedImages).forEach(([tag, images]) => {
        brandImagesFormatted += `AI Tag ${tag}\n`;
        images.forEach((img, index) => {
          brandImagesFormatted += `Option ${index + 1}:\n`;
          brandImagesFormatted += `url: ${img.url}\n`;
          brandImagesFormatted += `Size: Width: ${img.width} and Height: ${img.height}\n`;
        });
        brandImagesFormatted += '\n';
      });
    }

    // Get unlayer components with organization-specific overrides
    let componentLibrary = '[]';
    let componentLibraryData: Array<{
      name: string;
      description: string;
      type: string;
	  active: boolean;
      editableFields: any;
    }> = [];

    try {
      // Get global components
      const components = await this.unlayerComponentRepository.find( { where: { orgId: null as any } } );
	  const orgComponents = await this.unlayerComponentRepository.find( { where: { orgId: orgId } } );

      if (orgComponents && orgComponents.length > 0) {
		  orgComponents.forEach(comp => {
			componentLibraryData.push({
			  name: comp.name,
			  description: comp.description || '',
			  type: comp.type || 'unlayer',
			  active: comp.active,
			  editableFields: JSON.parse(comp.editableFields || '{}')
			});
		  });

          components.forEach(comp => {
            // Check if this global component has been overridden
            const isOverridden = orgComponents.some(orgComp => orgComp.overrideId === comp.id);

            if (!isOverridden) {
              componentLibraryData.push({
                name: comp.name,
                description: comp.description || '',
                type: comp.type || 'unlayer',
                active: comp.active,
                editableFields: JSON.parse(comp.editableFields || '{}')
              });
            }
          });

      } else {
        // Use global components
        components.forEach(comp => {
          componentLibraryData.push({
            name: comp.name,
            description: comp.description || '',
            type: comp.type || 'unlayer',
			active: comp.active,
            editableFields: JSON.parse(comp.editableFields || '{}')
          });
        });
      }
    } catch (e) {
      console.error('Error formatting component library:', e);
    }

	//Filter out components that are not active
	componentLibraryData = componentLibraryData.filter(comp => comp.active);

    componentLibrary = JSON.stringify(componentLibraryData, null, 2);
	let componentLibrarySummary = componentLibraryData.map(comp =>
		`Name of Component: ${comp.name}\nDescription: ${comp.description}`
	).join('\n');

    return {
      storeDescription: organization?.description || 'A description of the store',
      storeLanguage: organization?.sampleLanguage || 'Hello, please make the text sound natural',
      storeAOV: `'${averageAov}'` || '$50',
      storeName: organization?.name || 'Store Name',
      segmentsData: segmentsData || 'Segment 1: Description 1\nSegment 2: Description 2',
      campaignTimeframe: 'Infer from provided information or default to 1 month',
      hasLoyalty: getSetting('hasLoyalty') === 'true',
      idealCustomer: getSetting('idealCustomer') || 'Please infer from the description',
      customerProblems: getSetting('customerProblems') || 'Please infer from the description',
      customerBehavior: getSetting('customerBehavior') || 'Please infer from the description',
      marketingSuccess: getSetting('marketingSuccess') || 'Please infer from the description',
      hasSubscriptions: getSetting('hasSubscriptions') === 'true',
      emailTemplates: getSetting('emailTemplates') || '[]',
      emailBranding: getSetting('emailBranding') || '',
      hasSMS: getSetting('hasSMS') === 'true',
      businessGoals: getSetting('businessGoals') || 'Goal 1: Maximize LTV\nGoal 2: Increase AOV\nGoal 3: Expand Market Share',
      bestSellers: getSetting('bestSellers') || 'Please infer from the description',
      productCategories: getSetting('productCategories') || 'Please infer from the description',
      brandCustomInstructions: getSetting('brandCustomInstructions') || '',
      planStrategy: getSetting('planStrategy') || DEFAULT_PLAN_STRATEGY,
      componentLibrary,
   	  componentLibrarySummary,
      brandImages: brandImagesFormatted || 'No categorized brand images available.',
      hasEmailGeneration: getSetting('hasEmailGeneration') === 'true' ? "This store has email generation activated" : "This store does not have email generation activated",
    };
  }

  /**
   * Retrieves an active prompt template by name
   *
   * @param name - Name of the template to retrieve
   * @returns Template content or null if not found
   */
  async getPromptTemplate(name: string): Promise<string | null> {
    const template = await this.promptTemplateRepository.findOne({
      where: {
        name: name,
        isActive: true
      }
    });

    return template?.content || null;
  }

  /**
   * Builds a prompt by applying a template and replacing all tags
   *
   * @param orgId - Organization ID to fetch data for
   * @param userPrompt - User-provided prompt text
   * @param templateName - Name of the template to use
   * @returns The fully processed prompt with all tags replaced
   */
  async buildPromptWithTemplate(orgId: number, userPrompt: string, templateName: string): Promise<string> {
    const promptContent = await this.getPromptTemplate(templateName);
    if (!promptContent) {
      throw new Error(`No active prompt template found for "${templateName}"`);
    }

    return this.replacePromptTags(promptContent, orgId, userPrompt);
  }

  /**
   * Determines if a prompt requires metrics data
   * Used to avoid unnecessary metric data fetching
   *
   * @param prompt - The prompt text to analyze
   * @returns True if the prompt contains metrics tags
   */
  promptRequiresMetrics(prompt: string): boolean {
    return prompt.includes('{TOP_5_SUBJECT_LINES}') ||
           prompt.includes('{KLAVIYO_CAMPAIGNS_ALL_L12}') ||
           prompt.includes('{KLAVIYO_CAMPAIGNS_12_MONTH_AGO}') ||
           prompt.includes('{LAST_50_CAMPAIGNS}') ||
           prompt.includes('{KLAVIYO_CAMPAIGN_SUMMARY}') ||
           prompt.includes('{KLAVIYO_LAST_WEEK}') ||
           prompt.includes('{KLAVIYO_LAST_MONTH}');
  }

  /**
   * Checks if provided extra data is missing required metrics
   *
   * @param extraMetricsData - The metrics data to check
   * @returns True if any required metrics are missing
   */
  extraDataMissingMetrics(extraMetricsData: any): boolean {
	return !extraMetricsData || ['top5SubjectLines', 'klaviyoCampaignsAll', 'klaviyoCampaigns12MonthAgo', 'last50Campaigns'].some(key => !extraMetricsData[key]);
  }

  /**
   * Gets metrics data for an organization, using cache when available
   * If cache misses, fetches from source and caches for future use
   *
   * @param orgId - Organization ID to fetch metrics for
   * @param prompt - The prompt that requires metrics
   * @returns Object containing all metrics data for tag replacement
   */
  async getMetricsData(orgId: number, prompt: string): Promise<any> {
    if (!this.promptRequiresMetrics(prompt)) {
      return {};
    }

    // Try to get cached metrics data first
    try {
      const cachedMetrics = await this.promptCacheService.getCachedMetricsData(orgId);
      if (cachedMetrics) {
        console.log(`Using cached metrics data for org ${orgId}`);
        return cachedMetrics;
      }
    } catch (error) {
      console.error(`Error retrieving cached metrics data for org ${orgId}:`, error);
      // Continue execution if cache retrieval fails
    }

    // Check if we need to fetch last 50 campaigns
    const needsLast50Campaigns = prompt.includes('{LAST_50_CAMPAIGNS}');

    // If no cached data, fetch it using the shared method
    const metricsData = await this.promptCacheService.fetchMetricsData(orgId, needsLast50Campaigns);

    if (!metricsData) {
      return {}; // Return empty object if fetch failed
    }

    // No need to cache the data as fetchMetricsData already returns data that cacheMetricsData would cache.
    // Since we're not using the result of fetchMetricsData directly in the cache, we'd have duplicate fetches.

    return metricsData;
  }

  /**
   * Gets metrics data with optional cache skipping
   */
  private async getMetricsDataWithCacheOption(orgId: number, prompt: string, skipCache?: boolean): Promise<any> {
    if (!this.promptRequiresMetrics(prompt)) {
      return {};
    }

    // Skip cache if requested or try to get cached metrics data first
    if (!skipCache) {
      try {
        const cachedMetrics = await this.promptCacheService.getCachedMetricsData(orgId);
        if (cachedMetrics) {
          console.log(`Using cached metrics data for org ${orgId}`);
          return cachedMetrics;
        }
      } catch (error) {
        console.error(`Error retrieving cached metrics data for org ${orgId}:`, error);
        // Continue execution if cache retrieval fails
      }
    } else {
      console.log(`Skipping cache for metrics data for org ${orgId}`);
    }

    // Check if we need to fetch last 50 campaigns
    const needsLast50Campaigns = prompt.includes('{LAST_50_CAMPAIGNS}');

    // Fetch fresh data
    const metricsData = await this.promptCacheService.fetchMetricsData(orgId, needsLast50Campaigns);

    if (!metricsData) {
      return {}; // Return empty object if fetch failed
    }

    return metricsData;
  }

  /**
   * Gets additional context data with optional cache skipping
   */
  private async getAdditionalContextDataWithCacheOption(orgId: number, prompt: string, skipCache?: boolean): Promise<any> {
    const organization = await this.organizationRepository.findById(orgId);
    const settings: OrganizationSettings[] = await this.organizationRepository.organizationSettings(orgId).find();
    const getSetting = (key: string) => settings.find(s => s.key === key)?.value;

    // Skip cache if requested or try to get cached context data
    let cachedContextData;
    if (!skipCache) {
      try {
        cachedContextData = await this.promptCacheService.getCachedContextData(orgId);
      } catch (error) {
        console.error(`Error retrieving cached context data for org ${orgId}:`, error);
        // Continue execution if cache retrieval fails
      }
    } else {
      console.log(`Skipping cache for context data for org ${orgId}`);
    }

    // Use cached averageAov if available and not skipping cache
    let averageAov;
    if (!skipCache && cachedContextData && cachedContextData.averageAov) {
      averageAov = cachedContextData.averageAov;
    } else {
      const getAverageAovQuery = await this.devDbDataSource.execute(`
        SELECT
          AVG(CASE
            WHEN aov IS NOT NULL AND aov > 0
            THEN aov
            END) as average_aov
        FROM raleonuseridentity
        WHERE
          orgid = $1
      `, [orgId]);

      averageAov = getAverageAovQuery[0]?.average_aov;

      // Cache the context data for future use (only if not skipping cache)
      if (!skipCache) {
        try {
          await this.promptCacheService.cacheContextData(orgId);
        } catch (error) {
          console.error(`Error caching context data for org ${orgId}:`, error);
          // Continue execution if caching fails
        }
      }
    }

    // Continue with the rest of the method exactly as in getAdditionalContextData
    // Gather segments
    const segments = await this.organizationSegmentRepository.find({
      where: {
        orgId: orgId
      }
    });

    const segmentsData = segments?.map(segment => `${segment.name}: ${segment.description}`).join('\n');

    // Fetch brand images with non-null and non-empty imageType
    const brandImages = await this.imageRepository.find({
      where: {
        orgId: orgId,
        imageType: {
          neq: '',
        }
      }
    });

    // Filter and format brand images
    const filteredBrandImages = brandImages.filter(image =>
      image.imageType !== null &&
      image.imageType !== undefined &&
      image.imageType !== '');

    // Group images by imageType (AI Tag)
    const groupedImages: Record<string, any[]> = {};
    filteredBrandImages.forEach(image => {
      const imageType = image.imageType || 'Uncategorized';
      if (!groupedImages[imageType]) {
        groupedImages[imageType] = [];
      }
      groupedImages[imageType].push({
        url: image.url,
        width: image.width || 0,
        height: image.height || 0
      });
    });

    // Format brand images data
    let brandImagesFormatted = filteredBrandImages.length > 0 ?
      '' : 'No brand images with AI Tags found. Please add AI Tags to your images in the Assets section.';

    if (filteredBrandImages.length > 0) {
      Object.entries(groupedImages).forEach(([tag, images]) => {
        brandImagesFormatted += `AI Tag ${tag}\n`;
        images.forEach((img, index) => {
          brandImagesFormatted += `Option ${index + 1}:\n`;
          brandImagesFormatted += `url: ${img.url}\n`;
          brandImagesFormatted += `Size: Width: ${img.width} and Height: ${img.height}\n`;
        });
        brandImagesFormatted += '\n';
      });
    }

    // Get unlayer components with organization-specific overrides
    let componentLibrary = '[]';
    let componentLibraryData: Array<{
      name: string;
      description: string;
      type: string;
	  active: boolean;
      editableFields: any;
    }> = [];

    try {
      // Get global components
      const components = await this.unlayerComponentRepository.find( { where: { orgId: null as any } } );
	  const orgComponents = await this.unlayerComponentRepository.find( { where: { orgId: orgId } } );

      if (orgComponents && orgComponents.length > 0) {
		  orgComponents.forEach(comp => {
			componentLibraryData.push({
			  name: comp.name,
			  description: comp.description || '',
			  type: comp.type || 'unlayer',
			  active: comp.active,
			  editableFields: JSON.parse(comp.editableFields || '{}')
			});
		  });

          components.forEach(comp => {
            // Check if this global component has been overridden
            const isOverridden = orgComponents.some(orgComp => orgComp.overrideId === comp.id);

            if (!isOverridden) {
              componentLibraryData.push({
                name: comp.name,
                description: comp.description || '',
                type: comp.type || 'unlayer',
                active: comp.active,
                editableFields: JSON.parse(comp.editableFields || '{}')
              });
            }
          });

      } else {
        // Use global components
        components.forEach(comp => {
          componentLibraryData.push({
            name: comp.name,
            description: comp.description || '',
            type: comp.type || 'unlayer',
			active: comp.active,
            editableFields: JSON.parse(comp.editableFields || '{}')
          });
        });
      }
    } catch (e) {
      console.error('Error formatting component library:', e);
    }

	//Filter out components that are not active
	componentLibraryData = componentLibraryData.filter(comp => comp.active);

    componentLibrary = JSON.stringify(componentLibraryData, null, 2);
	let componentLibrarySummary = componentLibraryData.map(comp =>
		`Name of Component: ${comp.name}\nDescription: ${comp.description}`
	).join('\n');

    return {
      storeDescription: organization?.description || 'A description of the store',
      storeLanguage: organization?.sampleLanguage || 'Hello, please make the text sound natural',
      storeAOV: `'${averageAov}'` || '$50',
      storeName: organization?.name || 'Store Name',
      segmentsData: segmentsData || 'Segment 1: Description 1\nSegment 2: Description 2',
      campaignTimeframe: 'Infer from provided information or default to 1 month',
      hasLoyalty: getSetting('hasLoyalty') === 'true',
      idealCustomer: getSetting('idealCustomer') || 'Please infer from the description',
      customerProblems: getSetting('customerProblems') || 'Please infer from the description',
      customerBehavior: getSetting('customerBehavior') || 'Please infer from the description',
      marketingSuccess: getSetting('marketingSuccess') || 'Please infer from the description',
      hasSubscriptions: getSetting('hasSubscriptions') === 'true',
      emailTemplates: getSetting('emailTemplates') || '[]',
      emailBranding: getSetting('emailBranding') || '',
      hasSMS: getSetting('hasSMS') === 'true',
      businessGoals: getSetting('businessGoals') || 'Goal 1: Maximize LTV\nGoal 2: Increase AOV\nGoal 3: Expand Market Share',
      bestSellers: getSetting('bestSellers') || 'Please infer from the description',
      productCategories: getSetting('productCategories') || 'Please infer from the description',
      brandCustomInstructions: getSetting('brandCustomInstructions') || '',
      planStrategy: getSetting('planStrategy') || DEFAULT_PLAN_STRATEGY,
      componentLibrary,
   	  componentLibrarySummary,
      brandImages: brandImagesFormatted || 'No categorized brand images available.',
      hasEmailGeneration: getSetting('hasEmailGeneration') === 'true' ? "This store has email generation activated" : "This store does not have email generation activated",
    };
  }

  /**
   * Replaces prompt tags with cache option
   */
  private async replacePromptTagsWithCacheOption(prompt: string, orgId: number, skipCache?: boolean, userPrompt?: string, campaign?: PlannerCampaign, task?: TaskWithRelations, extraMetricsData?: any): Promise<string> {
    const data = await this.getAdditionalContextDataWithCacheOption(orgId, prompt, skipCache);

	if (this.promptRequiresMetrics(prompt) && this.extraDataMissingMetrics(extraMetricsData)) {
		const klaviyoMetrics = await this.getMetricsDataWithCacheOption(orgId, prompt, skipCache);
		extraMetricsData = {
			...(extraMetricsData || {}),
			...klaviyoMetrics
		};
	}

    if(!data.emailBranding) {
      data.emailBranding = '';
    }

    if (campaign) {
      data.campaignType = campaign.type;
      data.campaignTargetSegment = campaign.targetSegment;
      data.campaignName = campaign.name;
      data.campaignDescription = campaign.description;
      data.campaignPromotionTitle = campaign.promotionTitle;
      data.campaignPromotionDescription = campaign.promotionDescription;
    }

    if (task) {
      data.taskType = task.taskType;
      data.taskDescription = task.description;
      data.taskScheduledDate = task.scheduledDate;
      data.emailBrief = task.taskSteps?.find(step => step.name === 'Content')?.data;
    }

    // Replace all available tags (same as original method)
    return prompt
      .replace(/{USER_INPUT}/g, userPrompt || '')
      .replace(/{BESTSELLERS}/g, data.bestSellers || '')
      .replace(/{CURRENT_DATE}/g, new Date().toISOString())
      .replace(/{CAMPAIGN_TYPE}/g, data.campaignType || '')
      .replace(/{CAMPAIGN_TARGET_SEGMENT}/g, data.campaignTargetSegment || '')
      .replace(/{CAMPAIGN_NAME}/g, data.campaignName || '')
      .replace(/{CAMPAIGN_DESCRIPTION}/g, data.campaignDescription || '')
      .replace(/{CAMPAIGN_PROMOTION_TITLE}/g, data.campaignPromotionTitle || '')
      .replace(/{CAMPAIGN_PROMOTION_DESCRIPTION}/g, data.campaignPromotionDescription || '')
      .replace(/{CAMPAIGN_TIMEFRAME}/g, data.campaignTimeframe || '1 month')
      .replace(/{STORE_NAME}/g, data.storeName || '')
      .replace(/{STORE_DESCRIPTION}/g, data.storeDescription || '')
      .replace(/{STORE_LANGUAGE}/g, data.storeLanguage || '')
      .replace(/{STORE_AOV}/g, data.storeAOV || '')
      .replace(/{PRODUCT_CATEGORIES}/g, data.productCategories || '')
      .replace(/{STORE_BESTSELLERS}/g, data.bestSellers || '')
      .replace(/{SEGMENTS_DATA}/g, data.segmentsData || '')
      .replace(/{BUSINESS_GOALS}/g, data.businessGoals || '')
      .replace(/{HAS_LOYALTY}/g, data.hasLoyalty ? 'This store has loyalty activated' : 'This store does not have loyalty activated')
      .replace(/{HAS_SMS}/g, data.hasSMS ? 'This store has SMS activated' : 'This store does not have SMS activated')
	  .replace(/{HAS_EMAIL_GEN}/g, data.hasEmailGeneration || 'This store does not have email generation activated')
      .replace(/{EMAIL_BRANDING}/g, data.emailBranding || '')
      .replace(/{IDEAL_CUSTOMER}/g, data.idealCustomer || '')
      .replace(/{CUSTOMER_PROBLEMS}/g, data.customerProblems || '')
      .replace(/{CUSTOMER_BEHAVIOR}/g, data.customerBehavior || '')
      .replace(/{EMAIL_BRIEF}/g, data.emailBrief || '')
      .replace(/{MARKETING_SUCCESS}/g, data.marketingSuccess || '')
      .replace(/{HAS_SUBSCRIPTIONS}/g, data.hasSubscriptions ? 'This store has subscriptions activated' : 'This store does not have subscriptions activated')
      .replace(/{BRAND_CUSTOM_INSTRUCTIONS}/g, data.brandCustomInstructions || '')
      .replace(/{PLAN_STRATEGY}/g, data.planStrategy || DEFAULT_PLAN_STRATEGY)
      .replace(/{INFLUENCER_COLLABORATION}/g, data.influencerCollaboration || '')
      .replace(/{EMAIL_TEMPLATES}/g, data.emailTemplates || '[]')
      .replace(/{COMPONENT_LIBRARY}/g, data.componentLibrary || '')
      .replace(/{COMPONENT_LIBRARY_SUMMARY}/g, data.componentLibrarySummary || '')
      .replace(/{BRAND_IMAGES}/g, data.brandImages || '')
      .replace(/{TOP_5_SUBJECT_LINES}/g, extraMetricsData?.top5SubjectLines || '')
      .replace(/{KLAVIYO_CAMPAIGNS_ALL_L12}/g, extraMetricsData?.klaviyoCampaignsAll || '')
      .replace(/{KLAVIYO_CAMPAIGNS_12_MONTH_AGO}/g, extraMetricsData?.klaviyoCampaigns12MonthAgo || '')
      .replace(/{LAST_50_CAMPAIGNS}/g, extraMetricsData?.last50Campaigns || '')
      .replace(/{KLAVIYO_CAMPAIGN_SUMMARY}/g, extraMetricsData?.klaviyoCampaignSummary || '')
      .replace(/{KLAVIYO_LAST_WEEK}/g, extraMetricsData?.klaviyoLastWeek || '')
      .replace(/{KLAVIYO_LAST_MONTH}/g, extraMetricsData?.klaviyoLastMonth || '')
      .replace(/{AVAILABLE_METRICS}/g, await this.getAvailableMetrics(orgId))
      .replace(/{MEMORY}/g, await this.getBrandMemories(orgId))
      .replace(/{CAMPAIGN_URLS}/g, await this.getCampaignURLs(orgId))
      .replace(/{TOOL_DATA_LOOKUP}/g, DEFAULT_TOOL_DATA_LOOKUP)
      .replace(/{TOOL_BEST_SELLERS}/g, DEFAULT_TOOL_BEST_SELLERS);
  }

  /**
   * Returns the current values for all prompt tags for an organization
   *
   * @param orgId - Organization ID to fetch values for
   * @param skipCache - Whether to skip cache and fetch fresh data
   */
  async getAllTagValues(orgId: number, skipCache?: boolean): Promise<Record<string, string>> {
    // Get metrics data once for all tags that need it
    const fullPrompt = PROMPT_TAGS.map(t => `{${t}}`).join('\n');
    const metricsData = await this.getMetricsDataWithCacheOption(orgId, fullPrompt, skipCache);

    const result: Record<string, string> = {};

    // Process each tag individually to avoid newline splitting issues
    for (const tag of PROMPT_TAGS) {
      try {
        const singleTagPrompt = `{${tag}}`;
        const replaced = await this.replacePromptTagsWithCacheOption(singleTagPrompt, orgId, skipCache, undefined, undefined, undefined, metricsData);
        // Clean up the result - remove extra whitespace and newlines for display
        result[tag] = replaced.trim();
      } catch (error) {
        console.error(`Error processing tag ${tag}:`, error);
        result[tag] = `Error processing tag: ${error.message}`;
      }
    }

    return result;
  }
}
