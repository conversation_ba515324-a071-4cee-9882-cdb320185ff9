import { BaseToolHandler } from '../toolHandlerService';
import { Emitter } from 'mitt';
import { getOrganizationSetting, updateOrganizationSetting } from '../organization-settings.js';

interface MemoryPayload {
  category: string;
  info: string;
}

interface StoredMemory {
  type: string;
  content: string;
  timestamp: string;
}

interface URLMemory {
  url: string;
  type: 'button' | 'image' | 'product' | 'collection' | 'general';
  description?: string;
  context?: string;
  timestamp: string;
}

export class MemoryToolHandler extends BaseToolHandler {
  public readonly tag = 'memory';

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register();
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
  }

  /**
   * Detects and extracts URLs from text content
   */
  private detectURLs(text: string): URLMemory[] {
    const urlRegex = /(https?:\/\/[^\s<>"']+)/gi;
    const urls: URLMemory[] = [];
    let match;

    while ((match = urlRegex.exec(text)) !== null) {
      const url = match[1];
      const type = this.categorizeURL(url, text);
      const context = this.extractURLContext(url, text);

      urls.push({
        url,
        type,
        context,
        timestamp: new Date().toISOString()
      });
    }

    return urls;
  }

  /**
   * Categorizes a URL based on its content and context
   */
  private categorizeURL(url: string, context: string): URLMemory['type'] {
    const lowerUrl = url.toLowerCase();
    const lowerContext = context.toLowerCase();

    // Check for product/collection URLs
    if (lowerUrl.includes('/products/') || lowerUrl.includes('/product/')) {
      return 'product';
    }
    if (lowerUrl.includes('/collections/') || lowerUrl.includes('/collection/')) {
      return 'collection';
    }

    // Check context for button/CTA usage
    if (lowerContext.includes('button') || lowerContext.includes('cta') ||
        lowerContext.includes('call to action') || lowerContext.includes('click here')) {
      return 'button';
    }

    // Check for image URLs
    if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?|$)/i) ||
        lowerContext.includes('image') || lowerContext.includes('photo')) {
      return 'image';
    }

    return 'general';
  }

  /**
   * Extracts context around a URL for better understanding
   */
  private extractURLContext(url: string, text: string): string {
    const urlIndex = text.indexOf(url);
    if (urlIndex === -1) return '';

    const start = Math.max(0, urlIndex - 50);
    const end = Math.min(text.length, urlIndex + url.length + 50);

    return text.substring(start, end).trim();
  }

  /**
   * Saves URLs to organization settings
   */
  private async saveURLsToStorage(urls: URLMemory[]): Promise<boolean> {
    if (urls.length === 0) return false;

    try {
      // Get existing URLs
      const existingURLsString = await getOrganizationSetting('campaignURLs');
      let existingURLs: URLMemory[] = [];

      if (existingURLsString) {
        try {
          existingURLs = JSON.parse(existingURLsString);
        } catch (parseError) {
          console.warn('Failed to parse existing URLs, starting fresh:', parseError);
          existingURLs = [];
        }
      }

      // Filter out duplicates
      const newURLs = urls.filter(newUrl =>
        !existingURLs.some(existing => existing.url === newUrl.url)
      );

      if (newURLs.length === 0) {
        console.log('URLMemoryHandler: All URLs already exist');
        return false;
      }

      // Add new URLs (most recent first)
      existingURLs.unshift(...newURLs);

      // Limit to last 100 URLs to prevent bloat
      const MAX_URLS = 100;
      if (existingURLs.length > MAX_URLS) {
        existingURLs = existingURLs.slice(0, MAX_URLS);
      }

      // Save back to organization settings
      await updateOrganizationSetting('campaignURLs', JSON.stringify(existingURLs));
      console.log('URLMemoryHandler: Successfully saved URLs to storage:', newURLs);
      return true;
    } catch (error) {
      console.error('URLMemoryHandler: Failed to save URLs to storage:', error);
      return false;
    }
  }

  private async saveMemoryToStorage(memory: MemoryPayload): Promise<boolean> {
    try {
      // Get existing memories
      const existingMemoriesString = await getOrganizationSetting('brandMemories');
      let existingMemories: StoredMemory[] = [];

      if (existingMemoriesString) {
        try {
          existingMemories = JSON.parse(existingMemoriesString);
        } catch (parseError) {
          console.warn('Failed to parse existing memories, starting fresh:', parseError);
          existingMemories = [];
        }
      }

      // Normalize content for duplicate detection (trim whitespace, ignore case)
      const normalizedNewContent = memory.info.trim().toLowerCase();

      // Check for duplicates - exact match on normalized content
      const isDuplicate = existingMemories.some(existing =>
        existing.content.trim().toLowerCase() === normalizedNewContent
      );

      if (isDuplicate) {
        console.log('MemoryToolHandler: Skipping duplicate memory:', memory.info);
        return false; // Indicate duplicate was skipped
      }

      // Map category to our new simplified types
      const typeMapping: Record<string, string> = {
        'info': 'info',
        'information': 'info',
        'brand info': 'info',
        'general': 'info',
        'about': 'info',
        'company': 'info',
        'text': 'text',
        'content': 'text',
        'messaging': 'text',
        'brand voice': 'preferences',
        'brand': 'preferences',
        'voice': 'preferences',
        'preference': 'preferences',
        'preferences': 'preferences',
        'style': 'preferences',
        'promotion': 'promotion',
        'promotional': 'promotion',
        'campaign': 'promotion',
        'offer': 'promotion',
        'discount': 'promotion',
        'email': 'email',
        'email template': 'email',
        'email design': 'email',
        'subject line': 'email',
        'brief': 'brief',
        'campaign brief': 'brief',
        'content brief': 'brief',
        'strategy': 'brief'
      };

      // Determine memory type from category
      const memoryType = typeMapping[memory.category.toLowerCase()] || 'info';

      // Create new memory object
      const newMemory: StoredMemory = {
        type: memoryType,
        content: memory.info.trim(),
        timestamp: new Date().toISOString()
      };

      // Add to existing memories (most recent first)
      existingMemories.unshift(newMemory);

      // Limit to last 50 memories to prevent bloat
      const MAX_MEMORIES = 50;
      if (existingMemories.length > MAX_MEMORIES) {
        existingMemories = existingMemories.slice(0, MAX_MEMORIES);
      }

      // Save back to organization settings
      await updateOrganizationSetting('brandMemories', JSON.stringify(existingMemories));
      console.log('MemoryToolHandler: Successfully saved memory to storage:', newMemory);
      return true; // Indicate successful save
    } catch (error) {
      console.error('MemoryToolHandler: Failed to save memory to storage:', error);
      return false; // Indicate save failed
    }
  }

  private fixInvalidJson(jsonString: string): string {
    try {
      // First try to parse as-is
      JSON.parse(jsonString);
      return jsonString;
    } catch (e) {
      console.log('MemoryToolHandler: Attempting to fix invalid JSON format');

      // Fix common JSON issues:
      // 1. Unquoted property names
      let fixed = jsonString
        .replace(/(\w+):/g, '"$1":')  // Add quotes around property names
        .replace(/,\s*}/g, '}')       // Remove trailing commas
        .replace(/,\s*]/g, ']');      // Remove trailing commas in arrays

      try {
        JSON.parse(fixed);
        console.log('MemoryToolHandler: Successfully fixed JSON format');
        return fixed;
      } catch (e2) {
        console.log('MemoryToolHandler: Could not fix JSON, attempting manual parsing');

        // Manual parsing as last resort
        const categoryMatch = jsonString.match(/category:\s*["']([^"']+)["']/i);
        const infoMatch = jsonString.match(/info:\s*["']([^"']+)["']/i);

        if (categoryMatch && infoMatch) {
          const manualJson = {
            category: categoryMatch[1],
            info: infoMatch[1]
          };
          console.log('MemoryToolHandler: Manual parsing successful:', manualJson);
          return JSON.stringify(manualJson);
        }

        // If all else fails, return original
        throw new Error('Could not parse or fix JSON');
      }
    }
  }

  async onEnd(): Promise<void> {
    if (!this.placeholderId) return;
    let rawContent = this.streamingContent.trim();

    // Strip <memory> tags if they exist in the content
    rawContent = rawContent.replace(/^<memory>\s*/, '').replace(/\s*<\/memory>$/, '');

    try {
      // Try to fix and parse the JSON
	  console.log('MemoryToolHandler received raw content:', rawContent);
      const fixedJson = this.fixInvalidJson(rawContent);
      const memory: MemoryPayload = JSON.parse(fixedJson);
      console.log('MemoryToolHandler received memory:', memory);

      // Validate that we have the required fields
      if (!memory.category || !memory.info) {
        throw new Error('Missing required fields: category and info');
      }

      // Detect and save URLs from the memory content
      const detectedURLs = this.detectURLs(memory.info);
      if (detectedURLs.length > 0) {
        console.log('MemoryToolHandler detected URLs:', detectedURLs);
        await this.saveURLsToStorage(detectedURLs);
      }

      // Try to save to storage
      const saved = await this.saveMemoryToStorage(memory);

      if (saved) {
        // Emit success message for UI display
        this.emitter.emit('chat:update-segment', {
          type: 'memory',
          sender: 'ai',
          memory,
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      } else {
        // Memory was a duplicate or failed to save
        this.emitter.emit('chat:update-segment', {
          type: 'text',
          sender: 'ai',
          content: 'Memory already exists in your brand knowledge base.',
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      }
    } catch (e) {
      console.error('MemoryToolHandler failed to parse content:', e);
      console.error('Raw content was:', rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error parsing memory: ${e.message}]`
      });
    }
    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId });
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
