import { BaseToolHandler } from '../toolHandlerService';
import { Emitter } from 'mitt';
import { getOrganizationSetting, updateOrganizationSetting } from '../organization-settings.js';

interface URLMemory {
  url: string;
  type: 'button' | 'image' | 'product' | 'collection' | 'general';
  description?: string;
  context?: string;
  timestamp: string;
}

export class URLMemoryToolHandler extends BaseToolHandler {
  public readonly tag = 'url_memory';

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register();
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
  }

  /**
   * Detects and extracts URLs from text content
   */
  private detectURLs(text: string): URLMemory[] {
    const urlRegex = /(https?:\/\/[^\s<>"']+)/gi;
    const urls: URLMemory[] = [];
    let match;

    while ((match = urlRegex.exec(text)) !== null) {
      const url = match[1];
      const type = this.categorizeURL(url, text);
      const context = this.extractURLContext(url, text);
      const description = this.generateURLDescription(url, type, context);
      
      urls.push({
        url,
        type,
        description,
        context,
        timestamp: new Date().toISOString()
      });
    }

    return urls;
  }

  /**
   * Categorizes a URL based on its content and context
   */
  private categorizeURL(url: string, context: string): URLMemory['type'] {
    const lowerUrl = url.toLowerCase();
    const lowerContext = context.toLowerCase();

    // Check for product/collection URLs
    if (lowerUrl.includes('/products/') || lowerUrl.includes('/product/')) {
      return 'product';
    }
    if (lowerUrl.includes('/collections/') || lowerUrl.includes('/collection/')) {
      return 'collection';
    }

    // Check context for button/CTA usage
    if (lowerContext.includes('button') || lowerContext.includes('cta') || 
        lowerContext.includes('call to action') || lowerContext.includes('click here') ||
        lowerContext.includes('shop now') || lowerContext.includes('buy now') ||
        lowerContext.includes('learn more') || lowerContext.includes('get started')) {
      return 'button';
    }

    // Check for image URLs
    if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?|$)/i) || 
        lowerContext.includes('image') || lowerContext.includes('photo') ||
        lowerContext.includes('picture') || lowerContext.includes('banner')) {
      return 'image';
    }

    return 'general';
  }

  /**
   * Extracts context around a URL for better understanding
   */
  private extractURLContext(url: string, text: string): string {
    const urlIndex = text.indexOf(url);
    if (urlIndex === -1) return '';

    const start = Math.max(0, urlIndex - 100);
    const end = Math.min(text.length, urlIndex + url.length + 100);
    
    return text.substring(start, end).trim();
  }

  /**
   * Generates a description for the URL based on its type and context
   */
  private generateURLDescription(url: string, type: URLMemory['type'], context: string): string {
    switch (type) {
      case 'product':
        return `Product page URL for use in email campaigns`;
      case 'collection':
        return `Collection page URL for category links`;
      case 'button':
        return `Call-to-action button URL`;
      case 'image':
        return `Image URL for email content`;
      default:
        return `General URL mentioned in campaign planning`;
    }
  }

  /**
   * Saves URLs to organization settings
   */
  private async saveURLsToStorage(urls: URLMemory[]): Promise<boolean> {
    if (urls.length === 0) return false;

    try {
      // Get existing URLs
      const existingURLsString = await getOrganizationSetting('campaignURLs');
      let existingURLs: URLMemory[] = [];

      if (existingURLsString) {
        try {
          existingURLs = JSON.parse(existingURLsString);
        } catch (parseError) {
          console.warn('Failed to parse existing URLs, starting fresh:', parseError);
          existingURLs = [];
        }
      }

      // Filter out duplicates
      const newURLs = urls.filter(newUrl => 
        !existingURLs.some(existing => existing.url === newUrl.url)
      );

      if (newURLs.length === 0) {
        console.log('URLMemoryHandler: All URLs already exist');
        return false;
      }

      // Add new URLs (most recent first)
      existingURLs.unshift(...newURLs);

      // Limit to last 100 URLs to prevent bloat
      const MAX_URLS = 100;
      if (existingURLs.length > MAX_URLS) {
        existingURLs = existingURLs.slice(0, MAX_URLS);
      }

      // Save back to organization settings
      await updateOrganizationSetting('campaignURLs', JSON.stringify(existingURLs));
      console.log('URLMemoryHandler: Successfully saved URLs to storage:', newURLs);
      return true;
    } catch (error) {
      console.error('URLMemoryHandler: Failed to save URLs to storage:', error);
      return false;
    }
  }

  /**
   * Static method to detect URLs in any text and save them
   */
  static async detectAndSaveURLs(text: string): Promise<URLMemory[]> {
    const handler = new URLMemoryToolHandler(null as any);
    const urls = handler.detectURLs(text);
    
    if (urls.length > 0) {
      await handler.saveURLsToStorage(urls);
    }
    
    return urls;
  }

  async onEnd(): Promise<void> {
    if (!this.placeholderId) return;
    const rawContent = this.streamingContent.trim();
    
    try {
      // Detect URLs in the content
      const detectedURLs = this.detectURLs(rawContent);
      
      if (detectedURLs.length > 0) {
        console.log('URLMemoryHandler detected URLs:', detectedURLs);
        const saved = await this.saveURLsToStorage(detectedURLs);
        
        if (saved) {
          // Emit success message for UI display
          this.emitter.emit('chat:update-segment', {
            type: 'url_memory',
            sender: 'ai',
            urls: detectedURLs,
            timestamp: new Date(),
            placeholderId: this.placeholderId,
          });
        }
      }
    } catch (e) {
      console.error('URLMemoryHandler failed to process content:', e);
    }
    
    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId });
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
