import { getOrganizationSetting, updateOrganizationSetting } from './organization-settings.js';

interface URLMemory {
  url: string;
  type: 'button' | 'image' | 'product' | 'collection' | 'general';
  description?: string;
  context?: string;
  timestamp: string;
}

export class URLDetectionService {
  /**
   * Detects and extracts URLs from text content
   */
  static detectURLs(text: string): URLMemory[] {
    const urlRegex = /(https?:\/\/[^\s<>"']+)/gi;
    const urls: URLMemory[] = [];
    let match;

    while ((match = urlRegex.exec(text)) !== null) {
      const url = match[1];
      const type = this.categorizeURL(url, text);
      const context = this.extractURLContext(url, text);
      const description = this.generateURLDescription(url, type, context);

      urls.push({
        url,
        type,
        description,
        context,
        timestamp: new Date().toISOString()
      });
    }

    return urls;
  }

  /**
   * Categorizes a URL based on its content and context
   */
  private static categorizeURL(url: string, context: string): URLMemory['type'] {
    const lowerUrl = url.toLowerCase();
    const lowerContext = context.toLowerCase();

    // Check for product/collection URLs
    if (lowerUrl.includes('/products/') || lowerUrl.includes('/product/')) {
      return 'product';
    }
    if (lowerUrl.includes('/collections/') || lowerUrl.includes('/collection/')) {
      return 'collection';
    }

    // Check context for button/CTA usage
    if (lowerContext.includes('button') || lowerContext.includes('cta') ||
        lowerContext.includes('call to action') || lowerContext.includes('click here') ||
        lowerContext.includes('shop now') || lowerContext.includes('buy now') ||
        lowerContext.includes('learn more') || lowerContext.includes('get started') ||
        lowerContext.includes('view product') || lowerContext.includes('add to cart')) {
      return 'button';
    }

    // Check for image URLs
    if (lowerUrl.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?|$)/i) ||
        lowerContext.includes('image') || lowerContext.includes('photo') ||
        lowerContext.includes('picture') || lowerContext.includes('banner') ||
        lowerContext.includes('hero image') || lowerContext.includes('product image')) {
      return 'image';
    }

    return 'general';
  }

  /**
   * Extracts context around a URL for better understanding
   */
  private static extractURLContext(url: string, text: string): string {
    const urlIndex = text.indexOf(url);
    if (urlIndex === -1) return '';

    const start = Math.max(0, urlIndex - 100);
    const end = Math.min(text.length, urlIndex + url.length + 100);

    return text.substring(start, end).trim();
  }

  /**
   * Generates a description for the URL based on its type and context
   */
  private static generateURLDescription(url: string, type: URLMemory['type'], context: string): string {
    switch (type) {
      case 'product':
        return `Product page URL for use in email campaigns`;
      case 'collection':
        return `Collection page URL for category links`;
      case 'button':
        return `Call-to-action button URL`;
      case 'image':
        return `Image URL for email content`;
      default:
        return `General URL mentioned in campaign planning`;
    }
  }

  /**
   * Saves URLs to organization settings
   */
  static async saveURLsToStorage(urls: URLMemory[]): Promise<boolean> {
    if (urls.length === 0) return false;

    try {
      // Get existing URLs
      const existingURLsString = await getOrganizationSetting('campaignURLs');
      let existingURLs: URLMemory[] = [];

      if (existingURLsString) {
        try {
          existingURLs = JSON.parse(existingURLsString);
        } catch (parseError) {
          console.warn('Failed to parse existing URLs, starting fresh:', parseError);
          existingURLs = [];
        }
      }

      // Filter out duplicates
      const newURLs = urls.filter(newUrl =>
        !existingURLs.some(existing => existing.url === newUrl.url)
      );

      if (newURLs.length === 0) {
        console.log('URLDetectionService: All URLs already exist');
        return false;
      }

      // Add new URLs (most recent first)
      existingURLs.unshift(...newURLs);

      // Limit to last 100 URLs to prevent bloat
      const MAX_URLS = 100;
      if (existingURLs.length > MAX_URLS) {
        existingURLs = existingURLs.slice(0, MAX_URLS);
      }

      // Save back to organization settings
      await updateOrganizationSetting('campaignURLs', JSON.stringify(existingURLs));
      console.log('URLDetectionService: Successfully saved URLs to storage:', newURLs);
      return true;
    } catch (error) {
      console.error('URLDetectionService: Failed to save URLs to storage:', error);
      return false;
    }
  }

  /**
   * Retrieves stored URLs from organization settings
   */
  static async getStoredURLs(): Promise<URLMemory[]> {
    try {
      const existingURLsString = await getOrganizationSetting('campaignURLs');
      if (!existingURLsString) return [];

      return JSON.parse(existingURLsString);
    } catch (error) {
      console.error('URLDetectionService: Failed to retrieve URLs:', error);
      return [];
    }
  }

  /**
   * Detects URLs in text and automatically saves them
   */
  static async detectAndSaveURLs(text: string): Promise<URLMemory[]> {
    const urls = this.detectURLs(text);

    if (urls.length > 0) {
      await this.saveURLsToStorage(urls);
      console.log('URLDetectionService: Detected and saved URLs:', urls);
    }

    return urls;
  }

  /**
   * Formats stored URLs for prompt context
   */
  static async formatURLsForPrompt(): Promise<string> {
    const urls = await this.getStoredURLs();

    if (urls.length === 0) {
      return 'No campaign URLs stored';
    }

    const formattedURLs = urls.map((urlData, index) => {
      return `${index + 1}. [${urlData.type.toUpperCase()}] ${urlData.url} - ${urlData.description}`;
    }).join('\n');

    return `Campaign URLs:\n${formattedURLs}`;
  }


}

export type { URLMemory };
