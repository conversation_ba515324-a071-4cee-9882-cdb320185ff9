import { ref, computed } from 'vue';
import { URLDetectionService, URLMemory } from '../services/urlDetectionService';

export function useURLManager() {
  const urls = ref<URLMemory[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const urlsByType = computed(() => {
    const grouped: Record<string, URLMemory[]> = {};
    urls.value.forEach(url => {
      if (!grouped[url.type]) {
        grouped[url.type] = [];
      }
      grouped[url.type].push(url);
    });
    return grouped;
  });

  const buttonURLs = computed(() => urlsByType.value.button || []);
  const productURLs = computed(() => urlsByType.value.product || []);
  const collectionURLs = computed(() => urlsByType.value.collection || []);
  const imageURLs = computed(() => urlsByType.value.image || []);
  const generalURLs = computed(() => urlsByType.value.general || []);

  const loadURLs = async () => {
    try {
      loading.value = true;
      error.value = null;
      urls.value = await URLDetectionService.getStoredURLs();
    } catch (err) {
      error.value = 'Failed to load URLs';
      console.error('Failed to load URLs:', err);
    } finally {
      loading.value = false;
    }
  };

  const addURL = async (urlData: Omit<URLMemory, 'timestamp'>) => {
    try {
      error.value = null;
      const newURL: URLMemory = {
        ...urlData,
        timestamp: new Date().toISOString()
      };
      
      await URLDetectionService.saveURLsToStorage([newURL]);
      await loadURLs(); // Refresh the list
      return true;
    } catch (err) {
      error.value = 'Failed to add URL';
      console.error('Failed to add URL:', err);
      return false;
    }
  };

  const updateURL = async (url: string, updates: Partial<URLMemory>) => {
    try {
      error.value = null;
      await URLDetectionService.updateURL(url, updates);
      await loadURLs(); // Refresh the list
      return true;
    } catch (err) {
      error.value = 'Failed to update URL';
      console.error('Failed to update URL:', err);
      return false;
    }
  };

  const removeURL = async (url: string) => {
    try {
      error.value = null;
      await URLDetectionService.removeURL(url);
      await loadURLs(); // Refresh the list
      return true;
    } catch (err) {
      error.value = 'Failed to remove URL';
      console.error('Failed to remove URL:', err);
      return false;
    }
  };

  const detectAndSaveURLs = async (text: string) => {
    try {
      error.value = null;
      const detectedURLs = await URLDetectionService.detectAndSaveURLs(text);
      if (detectedURLs.length > 0) {
        await loadURLs(); // Refresh the list
      }
      return detectedURLs;
    } catch (err) {
      error.value = 'Failed to detect and save URLs';
      console.error('Failed to detect and save URLs:', err);
      return [];
    }
  };

  const getURLsForPrompt = async () => {
    try {
      return await URLDetectionService.formatURLsForPrompt();
    } catch (err) {
      console.error('Failed to format URLs for prompt:', err);
      return 'Error retrieving campaign URLs';
    }
  };

  const findURLsByType = (type: URLMemory['type']) => {
    return urls.value.filter(url => url.type === type);
  };

  const findURLByPattern = (pattern: string) => {
    const regex = new RegExp(pattern, 'i');
    return urls.value.find(url => 
      regex.test(url.url) || 
      regex.test(url.description || '') ||
      regex.test(url.context || '')
    );
  };

  const getRecentURLs = (limit: number = 10) => {
    return [...urls.value]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  };

  const clearError = () => {
    error.value = null;
  };

  return {
    // State
    urls,
    loading,
    error,
    
    // Computed
    urlsByType,
    buttonURLs,
    productURLs,
    collectionURLs,
    imageURLs,
    generalURLs,
    
    // Methods
    loadURLs,
    addURL,
    updateURL,
    removeURL,
    detectAndSaveURLs,
    getURLsForPrompt,
    findURLsByType,
    findURLByPattern,
    getRecentURLs,
    clearError
  };
}

export type { URLMemory };
