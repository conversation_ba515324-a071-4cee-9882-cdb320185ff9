<template>
  <div class="url-quick-access">
    <div class="header">
      <h4>Available URLs</h4>
      <button @click="toggleExpanded" class="toggle-btn">
        {{ expanded ? 'Hide' : 'Show' }} ({{ urls.length }})
      </button>
    </div>
    
    <div v-if="expanded" class="url-list">
      <div v-if="loading" class="loading">
        Loading URLs...
      </div>
      
      <div v-else-if="urls.length === 0" class="empty">
        No URLs stored yet
      </div>
      
      <div v-else class="url-groups">
        <div v-for="(groupUrls, type) in urlsByType" :key="type" class="url-group">
          <h5 class="group-title">{{ formatType(type) }}</h5>
          <div class="url-items">
            <div 
              v-for="url in groupUrls" 
              :key="url.url" 
              class="url-item"
              @click="copyURL(url.url)"
              :title="`Click to copy: ${url.url}`"
            >
              <div class="url-text">
                {{ truncateURL(url.url) }}
              </div>
              <div v-if="url.description" class="url-desc">
                {{ url.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Copy feedback -->
    <div v-if="copyFeedback" class="copy-feedback">
      URL copied to clipboard!
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';
import { useURLManager } from '../composables/useURLManager';

export default defineComponent({
  name: 'URLQuickAccess',
  setup() {
    const { urls, loading, loadURLs, urlsByType } = useURLManager();
    const expanded = ref(false);
    const copyFeedback = ref(false);

    const toggleExpanded = () => {
      expanded.value = !expanded.value;
      if (expanded.value && urls.value.length === 0) {
        loadURLs();
      }
    };

    const formatType = (type: string) => {
      const typeMap: Record<string, string> = {
        button: 'Buttons/CTAs',
        product: 'Products',
        collection: 'Collections',
        image: 'Images',
        general: 'General'
      };
      return typeMap[type] || type;
    };

    const truncateURL = (url: string, maxLength: number = 50) => {
      if (url.length <= maxLength) return url;
      return url.substring(0, maxLength) + '...';
    };

    const copyURL = async (url: string) => {
      try {
        await navigator.clipboard.writeText(url);
        copyFeedback.value = true;
        setTimeout(() => {
          copyFeedback.value = false;
        }, 2000);
      } catch (err) {
        console.error('Failed to copy URL:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        copyFeedback.value = true;
        setTimeout(() => {
          copyFeedback.value = false;
        }, 2000);
      }
    };

    onMounted(() => {
      loadURLs();
    });

    return {
      urls,
      loading,
      expanded,
      copyFeedback,
      urlsByType,
      toggleExpanded,
      formatType,
      truncateURL,
      copyURL
    };
  }
});
</script>

<style scoped>
.url-quick-access {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin: 16px 0;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.toggle-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background: #e0e7ff;
}

.url-list {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.loading, .empty {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  padding: 20px;
}

.url-group {
  margin-bottom: 16px;
}

.url-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: 12px;
  font-weight: 600;
  color: #4b5563;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.url-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.url-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.url-item:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.url-text {
  font-size: 13px;
  color: #3b82f6;
  font-family: monospace;
  word-break: break-all;
}

.url-desc {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.copy-feedback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #059669;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}
</style>
