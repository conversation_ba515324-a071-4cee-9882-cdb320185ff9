<template>
  <div class="url-persistence-test">
    <h2>URL Persistence Test</h2>
    <p>This component tests the URL persistence workflow across different chat contexts.</p>

    <!-- Test Input Section -->
    <div class="test-section">
      <h3>1. Test URL Detection</h3>
      <p>Enter text with URLs to test automatic detection and storage:</p>
      <textarea
        v-model="testText"
        placeholder="Enter text with URLs, e.g., 'Check out our new product at https://example.com/products/new-item and use this button URL https://example.com/shop-now for the CTA'"
        rows="4"
        class="test-input"
      ></textarea>
      <button @click="testURLDetection" :disabled="!testText.trim()" class="btn-primary">
        Test URL Detection
      </button>
      
      <div v-if="detectionResults.length > 0" class="results">
        <h4>Detected URLs:</h4>
        <div v-for="(url, index) in detectionResults" :key="index" class="detected-url">
          <strong>{{ url.type.toUpperCase() }}:</strong> {{ url.url }}
          <br>
          <small>{{ url.description }}</small>
        </div>
      </div>
    </div>

    <!-- Stored URLs Display -->
    <div class="test-section">
      <h3>2. Stored URLs</h3>
      <button @click="refreshStoredURLs" class="btn-secondary">
        Refresh Stored URLs
      </button>
      
      <div v-if="loading" class="loading">Loading...</div>
      
      <div v-else-if="storedURLs.length === 0" class="empty">
        No URLs stored yet
      </div>
      
      <div v-else class="stored-urls">
        <div v-for="(url, index) in storedURLs" :key="index" class="stored-url">
          <div class="url-header">
            <span class="url-type" :class="`type-${url.type}`">{{ url.type.toUpperCase() }}</span>
            <span class="url-date">{{ formatDate(url.timestamp) }}</span>
          </div>
          <div class="url-link">{{ url.url }}</div>
          <div v-if="url.description" class="url-description">{{ url.description }}</div>
        </div>
      </div>
    </div>

    <!-- Prompt Context Test -->
    <div class="test-section">
      <h3>3. Prompt Context Test</h3>
      <p>Test how URLs are formatted for prompt context:</p>
      <button @click="testPromptContext" class="btn-secondary">
        Generate Prompt Context
      </button>
      
      <div v-if="promptContext" class="prompt-context">
        <h4>Prompt Context Output:</h4>
        <pre>{{ promptContext }}</pre>
      </div>
    </div>

    <!-- Manual URL Addition -->
    <div class="test-section">
      <h3>4. Manual URL Addition</h3>
      <div class="form-row">
        <input
          v-model="manualURL.url"
          type="url"
          placeholder="https://example.com"
          class="form-input"
        >
        <select v-model="manualURL.type" class="form-select">
          <option value="button">Button</option>
          <option value="product">Product</option>
          <option value="collection">Collection</option>
          <option value="image">Image</option>
          <option value="general">General</option>
        </select>
        <input
          v-model="manualURL.description"
          type="text"
          placeholder="Description"
          class="form-input"
        >
        <button @click="addManualURL" :disabled="!manualURL.url" class="btn-primary">
          Add URL
        </button>
      </div>
    </div>

    <!-- Clear All URLs -->
    <div class="test-section">
      <h3>5. Clear Test Data</h3>
      <button @click="clearAllURLs" class="btn-danger">
        Clear All URLs
      </button>
    </div>

    <!-- Status Messages -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { URLDetectionService, URLMemory } from '../services/urlDetectionService';

export default defineComponent({
  name: 'URLPersistenceTest',
  setup() {
    const testText = ref('');
    const detectionResults = ref<URLMemory[]>([]);
    const storedURLs = ref<URLMemory[]>([]);
    const loading = ref(false);
    const promptContext = ref('');
    const statusMessage = ref('');
    const statusType = ref('success');
    
    const manualURL = ref({
      url: '',
      type: 'general' as URLMemory['type'],
      description: ''
    });

    const showStatus = (message: string, type: 'success' | 'error' = 'success') => {
      statusMessage.value = message;
      statusType.value = type;
      setTimeout(() => {
        statusMessage.value = '';
      }, 3000);
    };

    const testURLDetection = async () => {
      try {
        const detected = await URLDetectionService.detectAndSaveURLs(testText.value);
        detectionResults.value = detected;
        
        if (detected.length > 0) {
          showStatus(`Detected and saved ${detected.length} URL(s)`);
          await refreshStoredURLs();
        } else {
          showStatus('No URLs detected in the text', 'error');
        }
      } catch (error) {
        console.error('URL detection test failed:', error);
        showStatus('URL detection failed', 'error');
      }
    };

    const refreshStoredURLs = async () => {
      try {
        loading.value = true;
        storedURLs.value = await URLDetectionService.getStoredURLs();
      } catch (error) {
        console.error('Failed to refresh stored URLs:', error);
        showStatus('Failed to load stored URLs', 'error');
      } finally {
        loading.value = false;
      }
    };

    const testPromptContext = async () => {
      try {
        promptContext.value = await URLDetectionService.formatURLsForPrompt();
        showStatus('Prompt context generated');
      } catch (error) {
        console.error('Failed to generate prompt context:', error);
        showStatus('Failed to generate prompt context', 'error');
      }
    };

    const addManualURL = async () => {
      try {
        const urlToAdd: URLMemory = {
          url: manualURL.value.url,
          type: manualURL.value.type,
          description: manualURL.value.description,
          timestamp: new Date().toISOString()
        };

        await URLDetectionService.saveURLsToStorage([urlToAdd]);
        showStatus('URL added successfully');
        
        // Reset form
        manualURL.value = {
          url: '',
          type: 'general',
          description: ''
        };
        
        await refreshStoredURLs();
      } catch (error) {
        console.error('Failed to add manual URL:', error);
        showStatus('Failed to add URL', 'error');
      }
    };

    const clearAllURLs = async () => {
      if (confirm('Are you sure you want to clear all stored URLs? This action cannot be undone.')) {
        try {
          // Clear by setting empty array
          await URLDetectionService.saveURLsToStorage([]);
          storedURLs.value = [];
          detectionResults.value = [];
          promptContext.value = '';
          showStatus('All URLs cleared');
        } catch (error) {
          console.error('Failed to clear URLs:', error);
          showStatus('Failed to clear URLs', 'error');
        }
      }
    };

    const formatDate = (timestamp: string) => {
      return new Date(timestamp).toLocaleDateString();
    };

    onMounted(() => {
      refreshStoredURLs();
    });

    return {
      testText,
      detectionResults,
      storedURLs,
      loading,
      promptContext,
      statusMessage,
      statusType,
      manualURL,
      testURLDetection,
      refreshStoredURLs,
      testPromptContext,
      addManualURL,
      clearAllURLs,
      formatDate
    };
  }
});
</script>

<style scoped>
.url-persistence-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.test-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  margin-bottom: 12px;
  font-family: inherit;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.form-input, .form-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  flex: 1;
  min-width: 150px;
}

.btn-primary, .btn-secondary, .btn-danger {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.results, .stored-urls {
  margin-top: 16px;
}

.detected-url, .stored-url {
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
}

.url-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.url-type {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

.type-button { background: #dbeafe; color: #1e40af; }
.type-product { background: #dcfce7; color: #166534; }
.type-collection { background: #fef3c7; color: #92400e; }
.type-image { background: #e0e7ff; color: #3730a3; }
.type-general { background: #f3f4f6; color: #374151; }

.url-date {
  font-size: 12px;
  color: #6b7280;
}

.url-link {
  color: #3b82f6;
  word-break: break-all;
  margin-bottom: 4px;
}

.url-description {
  color: #6b7280;
  font-size: 14px;
}

.prompt-context {
  margin-top: 16px;
}

.prompt-context pre {
  background: white;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  white-space: pre-wrap;
  font-size: 14px;
  max-height: 300px;
  overflow-y: auto;
}

.loading, .empty {
  text-align: center;
  color: #6b7280;
  padding: 20px;
}

.status-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 500;
  z-index: 1000;
}

.status-message.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-message.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}
</style>
