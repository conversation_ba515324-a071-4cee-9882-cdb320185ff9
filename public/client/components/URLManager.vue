<template>
  <div class="url-manager">
    <div class="header">
      <h3 class="title">Campaign URLs</h3>
      <p class="subtitle">Manage URLs that will be available across your email campaigns</p>
    </div>

    <!-- Add New URL Section -->
    <div class="add-url-section">
      <h4>Add New URL</h4>
      <div class="form-group">
        <label for="new-url">URL</label>
        <input
          id="new-url"
          v-model="newUrl.url"
          type="url"
          placeholder="https://example.com/product"
          class="form-input"
        />
      </div>
      <div class="form-group">
        <label for="url-type">Type</label>
        <select id="url-type" v-model="newUrl.type" class="form-select">
          <option value="button">Button/CTA</option>
          <option value="product">Product Page</option>
          <option value="collection">Collection Page</option>
          <option value="image">Image URL</option>
          <option value="general">General</option>
        </select>
      </div>
      <div class="form-group">
        <label for="url-description">Description</label>
        <input
          id="url-description"
          v-model="newUrl.description"
          type="text"
          placeholder="Brief description of this URL"
          class="form-input"
        />
      </div>
      <button @click="addURL" :disabled="!newUrl.url" class="btn-primary">
        Add URL
      </button>
    </div>

    <!-- URLs List -->
    <div class="urls-list">
      <h4>Stored URLs ({{ urls.length }})</h4>
      
      <div v-if="loading" class="loading">
        Loading URLs...
      </div>
      
      <div v-else-if="urls.length === 0" class="empty-state">
        <p>No URLs stored yet. Add URLs above to make them available in your email campaigns.</p>
      </div>
      
      <div v-else class="url-items">
        <div v-for="(urlData, index) in urls" :key="index" class="url-item">
          <div class="url-content">
            <div class="url-header">
              <span class="url-type" :class="`type-${urlData.type}`">
                {{ urlData.type.toUpperCase() }}
              </span>
              <span class="url-date">
                {{ formatDate(urlData.timestamp) }}
              </span>
            </div>
            <div class="url-link">
              <a :href="urlData.url" target="_blank" rel="noopener noreferrer">
                {{ urlData.url }}
              </a>
            </div>
            <div v-if="urlData.description" class="url-description">
              {{ urlData.description }}
            </div>
          </div>
          <div class="url-actions">
            <button @click="editURL(index)" class="btn-secondary">
              Edit
            </button>
            <button @click="removeURL(urlData.url)" class="btn-danger">
              Remove
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="editingIndex !== -1" class="modal-overlay" @click="cancelEdit">
      <div class="modal" @click.stop>
        <h4>Edit URL</h4>
        <div class="form-group">
          <label>URL</label>
          <input
            v-model="editingUrl.url"
            type="url"
            class="form-input"
            readonly
          />
        </div>
        <div class="form-group">
          <label>Type</label>
          <select v-model="editingUrl.type" class="form-select">
            <option value="button">Button/CTA</option>
            <option value="product">Product Page</option>
            <option value="collection">Collection Page</option>
            <option value="image">Image URL</option>
            <option value="general">General</option>
          </select>
        </div>
        <div class="form-group">
          <label>Description</label>
          <input
            v-model="editingUrl.description"
            type="text"
            class="form-input"
          />
        </div>
        <div class="modal-actions">
          <button @click="saveEdit" class="btn-primary">Save</button>
          <button @click="cancelEdit" class="btn-secondary">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { URLDetectionService, URLMemory } from '../services/urlDetectionService';

export default defineComponent({
  name: 'URLManager',
  setup() {
    const urls = ref<URLMemory[]>([]);
    const loading = ref(true);
    const editingIndex = ref(-1);
    const editingUrl = ref<URLMemory>({
      url: '',
      type: 'general',
      description: '',
      timestamp: ''
    });
    const newUrl = ref({
      url: '',
      type: 'general' as URLMemory['type'],
      description: ''
    });

    const loadURLs = async () => {
      try {
        loading.value = true;
        urls.value = await URLDetectionService.getStoredURLs();
      } catch (error) {
        console.error('Failed to load URLs:', error);
      } finally {
        loading.value = false;
      }
    };

    const addURL = async () => {
      if (!newUrl.value.url) return;

      try {
        const urlToAdd: URLMemory = {
          url: newUrl.value.url,
          type: newUrl.value.type,
          description: newUrl.value.description,
          timestamp: new Date().toISOString()
        };

        await URLDetectionService.saveURLsToStorage([urlToAdd]);
        await loadURLs();
        
        // Reset form
        newUrl.value = {
          url: '',
          type: 'general',
          description: ''
        };
      } catch (error) {
        console.error('Failed to add URL:', error);
      }
    };

    const removeURL = async (url: string) => {
      if (confirm('Are you sure you want to remove this URL?')) {
        try {
          await URLDetectionService.removeURL(url);
          await loadURLs();
        } catch (error) {
          console.error('Failed to remove URL:', error);
        }
      }
    };

    const editURL = (index: number) => {
      editingIndex.value = index;
      editingUrl.value = { ...urls.value[index] };
    };

    const saveEdit = async () => {
      try {
        await URLDetectionService.updateURL(editingUrl.value.url, {
          type: editingUrl.value.type,
          description: editingUrl.value.description
        });
        await loadURLs();
        cancelEdit();
      } catch (error) {
        console.error('Failed to update URL:', error);
      }
    };

    const cancelEdit = () => {
      editingIndex.value = -1;
      editingUrl.value = {
        url: '',
        type: 'general',
        description: '',
        timestamp: ''
      };
    };

    const formatDate = (timestamp: string) => {
      return new Date(timestamp).toLocaleDateString();
    };

    onMounted(() => {
      loadURLs();
    });

    return {
      urls,
      loading,
      newUrl,
      editingIndex,
      editingUrl,
      addURL,
      removeURL,
      editURL,
      saveEdit,
      cancelEdit,
      formatDate
    };
  }
});
</script>

<style scoped>
.url-manager {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.subtitle {
  color: #6b7280;
  margin: 0;
}

.add-url-section {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.add-url-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
}

.form-input, .form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary, .btn-secondary, .btn-danger {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.urls-list h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.loading, .empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.url-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  background: white;
}

.url-content {
  flex: 1;
  margin-right: 16px;
}

.url-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.url-type {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

.type-button { background: #dbeafe; color: #1e40af; }
.type-product { background: #dcfce7; color: #166534; }
.type-collection { background: #fef3c7; color: #92400e; }
.type-image { background: #e0e7ff; color: #3730a3; }
.type-general { background: #f3f4f6; color: #374151; }

.url-date {
  font-size: 12px;
  color: #6b7280;
}

.url-link a {
  color: #3b82f6;
  text-decoration: none;
  word-break: break-all;
}

.url-link a:hover {
  text-decoration: underline;
}

.url-description {
  margin-top: 4px;
  color: #6b7280;
  font-size: 14px;
}

.url-actions {
  display: flex;
  gap: 8px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.modal h4 {
  margin: 0 0 16px 0;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
